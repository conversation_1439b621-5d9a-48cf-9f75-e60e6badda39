import { View, StyleSheet, SafeAreaView, ScrollView, Text, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { Pill, Check, Plus, X } from 'lucide-react-native';
import { useTheme } from '@/scr/context/themeContext';
import IconTitleInstructions from '@/scr/components/IconTitleInstructions';
import { BackNextButton } from '@/scr/components/backNextButton';
import { useUserTypesStore, useMedicationsStore, useOnboardingStore, useMedicalDevicesStore } from '@/scr/utils/onboardingStore';
import { router, useFocusEffect } from 'expo-router';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import Label from '@/scr/components/forms/Label';
import { BottomSheetModalProvider, BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import ComboBox, { ComboBoxItem } from '@/scr/components/forms/ComboBox';
import { medications } from '@/scr/data/medications';
import { medicalDevices } from '@/scr/data/medicalDevices';
import { useCompleteOnboarding } from '@/scr/hooks/completeOnboarding';

// Define a new type for items used in the ComboBox.
// This will hold all the necessary data for searching, displaying, and storing.
interface SearchableItem extends ComboBoxItem {
  type: 'medication' | 'device';
  displayLabel: string; // The name to show in the UI (e.g., "Humira")
  searchableLabel: string; // A combined string for searching (e.g., "humira adalimumab")
  genericName: string;
  originalId: string; // The original ID from the medication or device list
}

const Medication = () => {
  const { theme } = useTheme();
  const { getDiagnosedDiseases } = useUserTypesStore();
  const { 
    getMedicationsForDisease, 
    addMedicationToDisease, 
    removeMedicationFromDisease,
    createMedicationEntry,
    setMedicationsComplete 
  } = useMedicationsStore();
  const {
    getMedicalDevicesForDisease,
    addMedicalDeviceToDisease,
    removeMedicalDeviceFromDisease,
    createMedicalDeviceEntry,
  } = useMedicalDevicesStore();
  const { completeOnboarding, loading: isOnboardingCompleting } = useCompleteOnboarding();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(6); // Medication is step 6
    }, [setCurrentStep])
  );
  
  const [currentDiseaseId, setCurrentDiseaseId] = useState<string | number | null>(null);
  const [currentDiseaseName, setCurrentDiseaseName] = useState<string>('');
  const [shouldAutoFocus, setShouldAutoFocus] = useState(false);

  // Transform the raw medication and device data into a flat list suitable for the ComboBox.
  // Each brand name becomes a separate, searchable item.
  const comboBoxItems = useMemo((): SearchableItem[] => {
    const medicationItems = medications.flatMap((med): SearchableItem[] => {
      const genericLower = med.genericName.toLowerCase();
      if (med.brandNames && med.brandNames.length > 0) {
        return med.brandNames.map(brandName => {
          const brandLower = brandName.toLowerCase();
          return {
            id: `medication-${med.id}-${brandName}`, // Unique ID for each brand
            type: 'medication',
            label: `${brandLower} ${genericLower}`, // Combined for searching, but not displayed
            displayLabel: brandName,
            searchableLabel: `${brandLower} ${genericLower}`,
            genericName: med.genericName,
            originalId: med.id,
          };
        });
      }
      // Fallback for medications with no brand names
      return [{
        id: `medication-${med.id}`,
        type: 'medication',
        label: genericLower,
        displayLabel: med.genericName,
        searchableLabel: genericLower,
        genericName: med.genericName,
        originalId: med.id,
      }];
    });

    const deviceItems = medicalDevices.flatMap((dev): SearchableItem[] => {
      const genericLower = dev.genericName.toLowerCase();
      const localizedLower = Object.values(dev.localizedNames || {}).join(' ').toLowerCase();

      if (dev.brandNames && dev.brandNames.length > 0) {
        return dev.brandNames.map(brandName => {
          const brandLower = brandName.toLowerCase();
          return {
            id: `device-${dev.id}-${brandName}`, // Unique ID for each brand
            type: 'device',
            label: `${brandLower} ${genericLower} ${localizedLower}`,
            displayLabel: brandName,
            searchableLabel: `${brandLower} ${genericLower} ${localizedLower}`,
            genericName: dev.genericName,
            originalId: dev.id,
          };
        });
      }
      return [{
        id: `device-${dev.id}`,
        type: 'device',
        label: `${genericLower} ${localizedLower}`,
        displayLabel: dev.genericName,
        searchableLabel: `${genericLower} ${localizedLower}`,
        genericName: dev.genericName,
        originalId: dev.id,
      }];
    });

    return [...medicationItems, ...deviceItems];
  }, []);

  // Bottom sheet ref and snap points
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['95%'], []);

  // Backdrop render function
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // Handle modal animation to trigger auto-focus
  const handleModalAnimate = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex === 0) { // Modal is opening
      setShouldAutoFocus(true);
    }
  }, []);

  // Get diseases where user is a diagnosed patient
  const diagnosedDiseases = useMemo(() => {
    return getDiagnosedDiseases();
  }, [getDiagnosedDiseases]);

  // Get current medications for the selected disease from the store
  // and map them back to the ComboBox item format for display.
  const getCurrentMedicationsForDisease = useCallback((diseaseId: string | number): SearchableItem[] => {
    const medicationEntries = getMedicationsForDisease(diseaseId);
    return medicationEntries.map(entry => {
      // The ComboBox needs a fully-formed item. We find the original medication
      // details from our transformed list using the unique ID.
      const comboItem = comboBoxItems.find(c => c.id === entry.medication.id);
      // We return the found item, or a default representation if not found.
      return comboItem || {
        id: entry.medication.id,
        type: 'medication',
        label: entry.medication.label,
        displayLabel: entry.medication.label,
        searchableLabel: entry.medication.label.toLowerCase(),
        genericName: '', 
        originalId: '',
      };
    });
  }, [getMedicationsForDisease, comboBoxItems]);

  const getCurrentDevicesForDisease = useCallback((diseaseId: string | number): SearchableItem[] => {
    const deviceEntries = getMedicalDevicesForDisease(diseaseId);
    return deviceEntries.map(entry => {
      const comboItem = comboBoxItems.find(c => c.id === entry.device.id);
      return comboItem || {
        id: entry.device.id,
        type: 'device',
        label: entry.device.label,
        displayLabel: entry.device.label,
        searchableLabel: entry.device.label.toLowerCase(),
        genericName: '',
        originalId: '',
      };
    });
  }, [getMedicalDevicesForDisease, comboBoxItems]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s4,
    },
    diseasesScrollContainer: {
      maxHeight: 400,
    },
    diseaseItem: {
      marginBottom: theme.spacing.spacing.s4,
    },
    diseaseName: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s2,
    },
    diseaseContentRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'flex-start',
      gap: theme.spacing.spacing.s2,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.Background.background200,
      marginVertical: theme.spacing.spacing.s4,
    },
    emptyStateText: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text600,
      textAlign: 'center',
      marginTop: theme.spacing.spacing.s4,
    },
    // Bottom Sheet Styles
    bottomSheetContainer: {
      backgroundColor: theme.colors.Background.background0,
      paddingVertical: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s4,
      flex: 1,
    },
    bottomSheetTopRow: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginBottom: theme.spacing.spacing.s3,
    },
    bottomSheetTitle: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s4,
    },
    closeButton: {
      backgroundColor: theme.colors.Background.background100,
      paddingHorizontal: theme.spacing.spacing.s3,
      paddingVertical: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.lg,
    },
    closeButtonText: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text900,
    },
    comboBoxContainer: {
      flex: 1,
    },
    // Styles for custom list item rendering
    listItem: {
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Background.background100,
    },
    selectedListItem: {
      backgroundColor: theme.colors.Primary.primary50,
    },
    listItemText: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text800
    },
  }), [theme]);

  const handleBack = () => {
    router.back();
  };

  const handleNext = async () => {
    // Mark medications as complete in the store
    setMedicationsComplete();
    console.log('Medication selection stored, attempting to complete onboarding...');

    const success = await completeOnboarding();

    if (success) {
      console.log('Onboarding successfully completed on backend.');
      // Complete onboarding and navigate to main app
      router.replace('/');
    } else {
      console.error('Failed to complete onboarding on the backend.');
      // TODO: Show an alert to the user
      Alert.alert('Error', 'Failed to complete onboarding on the backend.');
    }
  };

  const handleAddMedication = (diseaseId: string | number, diseaseName: string) => {
    setCurrentDiseaseId(diseaseId);
    setCurrentDiseaseName(diseaseName);
    setShouldAutoFocus(false); // Reset auto-focus state
    bottomSheetModalRef.current?.present();
  };

  const handleSelectionChange = (selectedItems: SearchableItem[]) => {
    if (currentDiseaseId === null) return;

    // Separate selected items by type
    const selectedMedications = selectedItems.filter(item => item.type === 'medication');
    const selectedDevices = selectedItems.filter(item => item.type === 'device');

    // --- Handle Medications ---
    const currentMedicationEntries = getMedicationsForDisease(currentDiseaseId);
    const currentMedicationIds = new Set(currentMedicationEntries.map(entry => entry.medication.id));
    
    const newMedications = selectedMedications.filter(item => !currentMedicationIds.has(item.id));
    
    const selectedMedicationIds = new Set(selectedMedications.map(item => item.id));
    const medicationsToRemove = currentMedicationEntries.filter(entry => !selectedMedicationIds.has(entry.medication.id));

    newMedications.forEach(medication => {
      const medicationEntry = createMedicationEntry({
        id: medication.id,
        label: medication.displayLabel,
      });
      addMedicationToDisease(String(currentDiseaseId), medicationEntry);
    });

    medicationsToRemove.forEach(entry => {
      removeMedicationFromDisease(String(currentDiseaseId), entry.medication.id);
    });

    // --- Handle Medical Devices ---
    const currentDeviceEntries = getMedicalDevicesForDisease(currentDiseaseId);
    const currentDeviceIds = new Set(currentDeviceEntries.map(entry => entry.device.id));

    const newDevices = selectedDevices.filter(item => !currentDeviceIds.has(item.id));

    const selectedDeviceIds = new Set(selectedDevices.map(item => item.id));
    const devicesToRemove = currentDeviceEntries.filter(entry => !selectedDeviceIds.has(entry.device.id));

    newDevices.forEach(device => {
      const deviceEntry = createMedicalDeviceEntry({
        id: device.id,
        label: device.displayLabel,
      });
      addMedicalDeviceToDisease(String(currentDiseaseId), deviceEntry);
    });

    devicesToRemove.forEach(entry => {
      removeMedicalDeviceFromDisease(String(currentDiseaseId), entry.device.id);
    });
  };

  const handleRemoveMedication = (diseaseId: string | number, medicationId: string | number) => {
    removeMedicationFromDisease(String(diseaseId), String(medicationId));
  };

  const handleRemoveDevice = (diseaseId: string | number, deviceId: string | number) => {
    removeMedicalDeviceFromDisease(String(diseaseId), String(deviceId));
  };

  const handleCloseBottomSheet = () => {
    setShouldAutoFocus(false); // Reset auto-focus state
    bottomSheetModalRef.current?.dismiss();
  };

  // If no diagnosed diseases, redirect back (shouldn't happen with proper routing)
  if (diagnosedDiseases.length === 0) {
    console.warn('No diagnosed diseases found, redirecting to complete onboarding');
    router.replace('/');
    return null;
  }

  return (
    <BottomSheetModalProvider>
      <ImageBackgroundComponent style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            behavior="padding"
            style={styles.keyboardAvoidingContainer}
          >
            <View style={styles.content}>
              <View style={styles.mainContent}>
                <View style={styles.centeredPageContentWrapper}>
                  <IconTitleInstructions
                    icon={Pill}
                    title="Your treatments."
                    instructions="Select any medications or medical devices you are currently using for your diagnosed conditions."
                    iconSize={32}
                  />
                  
                  <View>
                    <ScrollView 
                      style={styles.diseasesScrollContainer}
                      showsVerticalScrollIndicator={false}
                      contentContainerStyle={{ paddingBottom: theme.spacing.spacing.s2 }}
                    >
                      {diagnosedDiseases.map((disease, index) => {
                        const diseaseId = disease.id;
                        const medicationEntries = getMedicationsForDisease(diseaseId);
                        const deviceEntries = getMedicalDevicesForDisease(diseaseId);
                        
                        return (
                          <View key={diseaseId}>
                            <View style={styles.diseaseItem}>
                              <Text style={styles.diseaseName}>
                                {disease.label}
                              </Text>
                              
                              <View style={styles.diseaseContentRow}>
                                <Label
                                  text="Add"
                                  variant="solid"
                                  colorScheme='neutral'
                                  iconLeft={<Plus size={16} color={theme.colors.Text.text0} />}
                                  onPress={() => handleAddMedication(diseaseId, disease.label)}
                                />
                                
                                {/* Display selected medications for this disease */}
                                {medicationEntries.map((medicationEntry) => (
                                  <Label
                                    key={medicationEntry.medication.id}
                                    text={medicationEntry.medication.label}
                                    variant="solid"
                                    colorScheme="muted"
                                    onRemove={() => handleRemoveMedication(diseaseId, medicationEntry.medication.id)}
                                  />
                                ))}

                                {/* Display selected devices for this disease */}
                                {deviceEntries.map((deviceEntry) => (
                                  <Label
                                    key={deviceEntry.device.id}
                                    text={deviceEntry.device.label}
                                    variant="solid"
                                    colorScheme="muted"
                                    onRemove={() => handleRemoveDevice(diseaseId, deviceEntry.device.id)}
                                  />
                                ))}
                              </View>
                            </View>
                            {index < diagnosedDiseases.length - 1 && (
                              <View style={styles.divider} />
                            )}
                          </View>
                        );
                      })}
                   
                    </ScrollView>
                  </View>
                  
                </View>
                
                <BackNextButton
                  onBackPress={handleBack}
                  onNextPress={handleNext}
                  nextTitle="Complete"
                  nextInactive={isOnboardingCompleting} 
                  nextIcon={<Check size={18} color={theme.colors.Text.text0} />}
                  nextBackgroundColor={theme.colors.Primary.primary500}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>

        {/* Bottom Sheet Modal for Medication Selection */}
        <BottomSheetModal
          ref={bottomSheetModalRef}
          snapPoints={snapPoints}
          enablePanDownToClose={true}
          enableDynamicSizing={false}
          backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
          backdropComponent={renderBackdrop}
          onAnimate={handleModalAnimate}
          handleIndicatorStyle={{
            backgroundColor: theme.colors.Background.background900,
          }}
        >
          <View style={{ flex: 1 }}>
            <BottomSheetView style={styles.bottomSheetContainer}>
              <View style={styles.bottomSheetTopRow}>
                <TouchableOpacity style={styles.closeButton} onPress={handleCloseBottomSheet} activeOpacity={0.7}>
                  <Text style={styles.closeButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.bottomSheetTitle}>
                Add medications or devices for {currentDiseaseName}
              </Text>
              
              <View style={styles.comboBoxContainer}>
                <ComboBox<SearchableItem>
                  data={comboBoxItems}
                  onSelectionChange={handleSelectionChange}
                  labelColorScheme="primary"
                  placeholder="Search for medications or devices..."
                  multiple={true}
                  selectedItems={
                    currentDiseaseId 
                      ? [...getCurrentMedicationsForDisease(currentDiseaseId), ...getCurrentDevicesForDisease(currentDiseaseId)] 
                      : []
                  }
                  autoFocus={shouldAutoFocus}
                  // Custom render function to display the brand/generic name in the dropdown list
                  renderListItem={(item, isSelected, onPress) => (
                    <TouchableOpacity
                      style={[styles.listItem, isSelected && styles.selectedListItem]}
                      onPress={onPress}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.listItemText}>{item.displayLabel}</Text>
                    </TouchableOpacity>
                  )}
                  // Custom render function to display the selected item pill
                  renderSelectedItem={(item, onRemove) => (
                    <Label
                      text={item.displayLabel}
                      onRemove={onRemove}
                      colorScheme="primary"
                      variant="solid"
                    />
                  )}
                />
              </View>
            </BottomSheetView>
          </View>
        </BottomSheetModal>
      </ImageBackgroundComponent>
    </BottomSheetModalProvider>
  );
};

export default Medication;
