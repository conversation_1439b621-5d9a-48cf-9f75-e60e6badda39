import React, { useState, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { CircleUserRound } from 'lucide-react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { useTheme } from '../../../scr/context/themeContext';
import { BackNextButton } from '../../../scr/components/backNextButton';
import IconTitleInstructions from '../../../scr/components/IconTitleInstructions';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { usePersonalInfoStore, useOnboardingStore } from '../../../scr/utils/onboardingStore';
import { ImageBackgroundComponent } from '../../../scr/components/onboarding/imageBackground';

const NameScreen = () => {
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  
  const router = useRouter();
  const { theme } = useTheme();


  // Use the onboarding store for personal info management
  const { personalInfo, updatePersonalInfo, isPersonalInfoValid } = usePersonalInfoStore();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(1); // Name is step 1
    }, [setCurrentStep])
  );

  // Validation function for name fields only
  const isNameValid = useMemo(() => {
    return personalInfo.firstName.trim() !== '' && personalInfo.lastName.trim() !== '';
  }, [personalInfo.firstName, personalInfo.lastName]);

  // Create theme-aware StyleSheet
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s4,
    },
    inputContainer: {
      width: '100%',
      gap: 6,
    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
    },
    required: {
      color: '#ff4757',
    },
    inputWrapper: {
      position: 'relative',
      width: '100%',
    },
    input: {
      fontSize: theme.typography.textSize.sm,
      color: theme.colors.Text.text950,
      paddingVertical: theme.spacing.spacing.s2,
      paddingRight: theme.spacing.spacing.s4,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: theme.typography.textSize.xs,
      marginTop: theme.spacing.spacing.s1,
    },
  }), [theme]);

  const handleBack = () => {
    router.back();
  };

  const handleNext = () => {
    setHasAttemptedSubmit(true);
    if (!isNameValid) {
      return;
    }

    // Navigate to info screen - data is already in the store
    router.push('/(auth)/(onboarding)/info');
  };

  const handleFirstNameChange = (text: string) => {
    updatePersonalInfo({ firstName: text });
  };

  const handleLastNameChange = (text: string) => {
    updatePersonalInfo({ lastName: text });
  };

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior="padding"
          keyboardVerticalOffset={70}
          style={styles.keyboardAvoidingContainer}
        >
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.centeredPageContentWrapper}>
                <IconTitleInstructions
                  icon={CircleUserRound}
                  title="Tell us about yourself"
                  instructions=""
                  iconSize={32}
                />

                <View style={styles.inputContainer}>
                  <Text style={styles.label}>
                    First names<Text style={styles.required}>*</Text>
                  </Text>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter first names"
                      placeholderTextColor={theme.colors.Text.text500}
                      value={personalInfo.firstName}
                      onChangeText={handleFirstNameChange}
                      autoCapitalize="words"
                      autoComplete="given-name"
                    />
                  </View>
                  {hasAttemptedSubmit && personalInfo.firstName.trim() === '' && (
                    <Text style={styles.errorText}>First names are required</Text>
                  )}
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.label}>
                    Last name<Text style={styles.required}>*</Text>
                  </Text>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter last name"
                      placeholderTextColor={theme.colors.Text.text500}
                      value={personalInfo.lastName}
                      onChangeText={handleLastNameChange}
                      autoCapitalize="words"
                      autoComplete="family-name"
                    />
                  </View>
                  {hasAttemptedSubmit && personalInfo.lastName.trim() === '' && (
                    <Text style={styles.errorText}>Last name is required</Text>
                  )}
                </View>
              </View>

              <BackNextButton
                onBackPress={handleBack}
                onNextPress={handleNext}
                nextInactive={!isNameValid}
                disabled={false}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
};

export default NameScreen;
