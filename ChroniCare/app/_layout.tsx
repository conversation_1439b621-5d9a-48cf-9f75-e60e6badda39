import { Stack, useRouter, useSegments } from 'expo-router';
import { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { OnboardingProvider, useOnboarding } from '../scr/context/onboardingContext';
import { ThemeProvider } from '../scr/context/themeContext';
import { AuthProvider, useAuth } from '../scr/context/authContext';
import { ApolloProvider } from '../scr/providers/ApolloProvider';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { UserProvider, useUser } from '../scr/context/userContext';
import { NetworkErrorScreen } from '../scr/components/NetworkErrorScreen';


function RootLayoutContent() {
	const router = useRouter();
	const segments = useSegments();
	const { hasCompletedOnboarding, isLoading: onboardingLoading } = useOnboarding();
	const { user, initializing } = useAuth();
	const { refetchUser, networkError } = useUser();
	const [isRetrying, setIsRetrying] = useState(false);

	useEffect(() => {
		if (initializing || onboardingLoading) return;

		// Don't run navigation logic if there's a network error
		if (networkError) return;

		const inAuthGroup = segments[0] === '(auth)';
		const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');

		if (user) {
			if (hasCompletedOnboarding) {
				if (!inAuthGroup || inOnboardingFlow) {
					router.replace('/(auth)/(tabs)/community');
				}
			} else {
				const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');
				if (!inOnboardingFlow) {
					router.replace('/(auth)/(onboarding)/consent');
				}
			}
		} else {
			const inPublicGroup = segments[0] === '(public)';
			if (!inPublicGroup) {
				if (hasCompletedOnboarding) {
					router.replace('/(public)/login');
				} else {
					router.replace('/(public)/intro');
				}
			}
		}
	}, [user, initializing, hasCompletedOnboarding, onboardingLoading, segments, router, networkError]);

	const handleRetry = async () => {
		if (isRetrying) return; // Prevent concurrent retries
		
		setIsRetrying(true);
		
		try {
			// Create a timeout promise that rejects after 10 seconds
			const timeoutPromise = new Promise((_, reject) => {
				setTimeout(() => reject(new Error('Request timeout')), 5000);
			});

			// Race between the refetch and timeout
			await Promise.race([refetchUser(), timeoutPromise]);
		} catch (error) {
			console.error('Retry failed:', error);
			// Even if retry fails, we still want to stop the loading state
			// The network error will persist and user can try again
		} finally {
			setIsRetrying(false);
		}
	};

	if (initializing || onboardingLoading) {
		return (
			<View
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					flex: 1
				}}
			>
				<ActivityIndicator size="large" />
			</View>
		);
	}

	if (user && networkError) {
		return <NetworkErrorScreen onRetry={handleRetry} isRetrying={isRetrying} />;
	}

	return (
		<Stack>
			<Stack.Screen name="index" options={{ headerShown: false }} />
			<Stack.Screen name="(public)" options={{ headerShown: false }} />
			<Stack.Screen name="(auth)" options={{ headerShown: false }} />
		</Stack>
	);
}

export default function RootLayout() {
	return (
		<GestureHandlerRootView style={{ flex: 1 }}>
			<KeyboardProvider>
				<ThemeProvider>
					<AuthProvider>
						<ApolloProvider>
							<UserProvider>
								<OnboardingProvider>
									<RootLayoutContent />
								</OnboardingProvider>
							</UserProvider>
						</ApolloProvider>
					</AuthProvider>
				</ThemeProvider>
			</KeyboardProvider>
		</GestureHandlerRootView>
	);
}