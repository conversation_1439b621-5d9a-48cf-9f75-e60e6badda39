{"project_info": {"project_number": "1056297800349", "project_id": "chronicare10", "storage_bucket": "chronicare10.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1056297800349:android:eb0fa579a1951265d77a74", "android_client_info": {"package_name": "io.chronicare.ChroniCare"}}, "oauth_client": [{"client_id": "1056297800349-hdc3bg2bbt193pe6ngo2ksgvt19oaock.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "io.chronicare.ChroniCare", "certificate_hash": "52fbff6f2abd4741f5e38a5379817580ca08699a"}}, {"client_id": "1056297800349-ttpd2ijh68g2pg1nd50erikpb45hj8r9.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "io.chronicare.ChroniCare", "certificate_hash": "5e8f16062ea3cd2c4a0d547876baa6f38cabf625"}}, {"client_id": "1056297800349-55idgf79hn7cp5htdifqhsohfdovjsu3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNeGedwkF0QyM1JvrlNnSgh89pcm5VXH8"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1056297800349-55idgf79hn7cp5htdifqhsohfdovjsu3.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1056297800349-sp8htepcij1fu64i395tei5gqur401sc.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "io.chronicare.ChroniCare"}}]}}}], "configuration_version": "1"}