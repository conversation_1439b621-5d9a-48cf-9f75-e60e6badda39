import { useMutation, gql } from '@apollo/client';
import { useUser } from '../context/userContext';
import { CREATE_COMMENT, GET_COMMENTS, GET_THREADS } from '../graphql/queries';
import { Alert } from 'react-native';
import { Comment } from '../components/community/comments/CommentCard';

interface CreateCommentInput {
  threadId: string;
  content: string;
  parentCommentId?: string;
}

export const useCreateComment = () => {
  const { user } = useUser();

  const [createCommentMutation, { loading, error }] = useMutation(CREATE_COMMENT, {
    // This will refetch both the comments for the thread and the thread list
    // ensuring the comment count is updated correctly.
    refetchQueries: (mutationResult) => {
      const communityId = mutationResult.data?.createComment?.communityId;
      const threadId = mutationResult.data?.createComment?.threadId;

      if (!threadId) return [];

      return [
        { query: GET_COMMENTS, variables: { threadId } },
        { query: GET_THREADS, variables: { limit: 10, communityId } },
      ];
    },
    onError: (error) => {
      Alert.alert('Error Creating Comment', error.message);
    },
  });

  const handleCreateComment = async (input: CreateCommentInput) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to comment.');
      return;
    }

    if (!input.content.trim()) {
      Alert.alert('Error', 'Comment cannot be empty.');
      return;
    }

    const authorInput = {
      authorId: user.id,
      displayName: user.displayName || user.firstName || user.email || 'Anonymous',
      condition: user.condition || '',
      userType: user.userType || '',
      photoURL: user.photoURL || '',
    };

    try {
      await createCommentMutation({
        variables: {
          input: {
            ...input,
            author: authorInput,
          },
        },
      });
    } catch (error) {
      console.error('Failed to create comment:', error);
      // Error is already handled by onError callback
    }
  };

  return { 
    createComment: handleCreateComment, 
    loading, 
    error 
  };
};

