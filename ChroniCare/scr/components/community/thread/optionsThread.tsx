import { View, Text, StyleSheet, Modal, TouchableOpacity, Pressable, Alert } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import { Trash2, Flag } from 'lucide-react-native';

interface ThreadOptionsMenuProps {
    isVisible: boolean;
    onClose: () => void;
    isAuthor: boolean;
    position: { x: number, y: number };
}

const ThreadOptionsMenu = ({ isVisible, onClose, isAuthor, position }: ThreadOptionsMenuProps) => {
    const { theme } = useTheme();

    const handleReport = () => {
        Alert.alert('Report', 'This functionality is not yet implemented.');
        onClose();
    };

    const handleDelete = () => {
        Alert.alert('Delete', 'This functionality is not yet implemented.');
        onClose();
    };

    const styles = useMemo(() => StyleSheet.create({
        overlay: {
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
        },
        menuContainer: {
            position: 'absolute',
            top: position.y,
            left: position.x,
            borderRadius: theme.spacing.borderRadius.xl,
            backgroundColor: theme.colors.Background.background0,
            width: 150,
            padding: theme.spacing.spacing.s2,
            elevation: 3,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 1.84,
        },
        menuItem: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: theme.spacing.spacing.s2,
            paddingHorizontal: theme.spacing.spacing.s2,
        },
        menuItemWithBorder: {
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.Background.background100,
        },
        menuItemText: {
            marginLeft: theme.spacing.spacing.s2,
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        },
        deleteText: {
            color: theme.colors.Indicator.error,
        }
    }), [theme, position]);

    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={isVisible}
            onRequestClose={onClose}
        >
            <Pressable style={styles.overlay} onPress={onClose}>
                <Pressable style={styles.menuContainer}>
                    <TouchableOpacity 
                        style={[styles.menuItem, isAuthor && styles.menuItemWithBorder]} 
                        onPress={handleReport}
                    >
                        <Flag color={theme.colors.Text.text900} size={20} />
                        <Text style={styles.menuItemText}>Report</Text>
                    </TouchableOpacity>
                    {isAuthor && (
                        <TouchableOpacity style={styles.menuItem} onPress={handleDelete}>
                            <Trash2 color={theme.colors.Indicator.error} size={20} />
                            <Text style={[styles.menuItemText, styles.deleteText]}>Delete</Text>
                        </TouchableOpacity>
                    )}
                </Pressable>
            </Pressable>
        </Modal>
    );
}

export default ThreadOptionsMenu;
