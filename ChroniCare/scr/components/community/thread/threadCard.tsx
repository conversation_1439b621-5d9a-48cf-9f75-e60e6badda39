import { View, Text, StyleSheet, Image, Pressable, TouchableOpacity, Platform, ScrollView, Modal, Dimensions } from 'react-native'
import React, { useMemo, useState, useRef } from 'react'
import { useTheme } from '@/scr/context/themeContext'
import Reactions from '../reactions/reactions';
import CommentCounter from '../comments/comment';
import TimeStamp from '../TimeStamp';
import { useRouter } from 'expo-router';
import { useFragment } from '@apollo/client';
import { THREAD_FRAGMENT } from '@/scr/graphql/fragments';
import { Ellipsis, X } from 'lucide-react-native';
import ThreadOptionsMenu from './optionsThread';
import { useUser } from '@/scr/context/userContext';

interface ThreadCardProps {
    threadId: string;
    isClickable?: boolean;
    truncateContent?: boolean;
}

const ThreadCard = ({ threadId, isClickable = true, truncateContent = false }: ThreadCardProps) => {
    const { theme } = useTheme();
    const { data: thread } = useFragment({
        from: {
            __typename: 'Thread',
            _id: threadId,
        },
        fragment: THREAD_FRAGMENT,
    });
    const router = useRouter();
    const { user } = useUser();
    const [menuVisible, setMenuVisible] = useState(false);
    const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
    const ellipsisRef = useRef<any>(null);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
    const [imageModalVisible, setImageModalVisible] = useState(false);
    const { width: screenWidth } = Dimensions.get('window');
    const [isNavigating, setIsNavigating] = useState(false);

    const handlePress = () => {
        if (isClickable && !isNavigating) {
            setIsNavigating(true);
            router.push(`/community/${threadId}`);
            setTimeout(() => {
                setIsNavigating(false);
            }, 1000); 
        }
    };

    const handleOptionsPress = () => {
        ellipsisRef.current?.measure((_fx: number, _fy: number, _width: number, height: number, px: number, py: number) => {
            const yPosition = Platform.OS === 'android' ? py : py + height;
            setMenuPosition({ x: px - 130, y: yPosition });
        });
        setMenuVisible(true);
    };

    const handleImagePress = (imageUrl: string, index: number) => {
        setSelectedImage(imageUrl);
        setSelectedImageIndex(index);
        setImageModalVisible(true);
    };

    const handleCloseImageModal = () => {
        setImageModalVisible(false);
        setSelectedImage(null);
    };

    const handleImageScroll = (event: any) => {
        const slideSize = event.nativeEvent.layoutMeasurement.width;
        const index = Math.floor(event.nativeEvent.contentOffset.x / slideSize);
        const maxIndex = thread?.imageUrls ? thread.imageUrls.length - 1 : 0;
        const clampedIndex = Math.max(0, Math.min(index, maxIndex));
        setSelectedImageIndex(clampedIndex);
    };

    const styles = useMemo(() => StyleSheet.create({
        postContainer: {
            gap: theme.spacing.spacing.s2,
        },
        authorContainer: {
            flexDirection: 'row',
        },
        authorImage: {
            width: 32,
            height: 32,
            borderRadius: 16,
            marginRight: theme.spacing.spacing.s2,
        },
        authorImagePlaceholder: {
            width: 32,
            height: 32,
            borderRadius: 16,
            marginRight: theme.spacing.spacing.s2,
            backgroundColor: theme.colors.Primary.primary500,
            justifyContent: 'center',
            alignItems: 'center',
        },
        authorImagePlaceholderText: {
            ...theme.textVariants.text('sm', 'bold'),
            color: '#FFFFFF',
        },
        authorInfo: {
            flex: 1,
        },
        authorMetaContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
        },
        authorName: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text900,
            marginRight: theme.spacing.spacing.s2,
        },
        authorDetails: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
        },
        authorDetailsDot: {
            marginHorizontal: theme.spacing.spacing.s1,
        },
        postTitle: {
            ...theme.textVariants.heading('sm', 'bold'),
            color: theme.colors.Text.text900,
        },
        postContent: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        },
        actionBar: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
        },
        imageContainer: {
            marginVertical: theme.spacing.spacing.s3,
        },
        imageRow: {
            flexDirection: 'row',
            gap: theme.spacing.spacing.s2,
        },
        singleImage: {
            width: '100%',
            height: 300,
            borderRadius: theme.spacing.borderRadius.lg,
            overflow: 'hidden',
        },
        multipleImages: {
            width: 200,
            height: 150,
            borderRadius: theme.spacing.borderRadius.lg,
            overflow: 'hidden',
        },
        threadImageContent: {
            width: '100%',
            height: '100%',
        },
        modalContainer: {
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            justifyContent: 'center',
            alignItems: 'center',
        },
        modalScrollView: {
            flex: 1,
            width: '100%',
        },
        modalImageContainer: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
        },
        modalImage: {
            width: '90%',
            height: '70%',
            borderRadius: theme.spacing.borderRadius.lg,
        },
        modalCloseButton: {
            position: 'absolute',
            top: 50,
            right: 20,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            borderRadius: 20,
            padding: 10,
            zIndex: 1,
        },
        imageIndicator: {
            position: 'absolute',
            top: 100,
            alignSelf: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 15,
        },
        imageIndicatorText: {
            color: theme.colors.Background.background0,
            fontSize: 14,
            fontWeight: '500',
        },
    }), [theme]);

    if (!thread || !thread.author) {
        return null;
    }

    const isAuthor = user?.id === thread.author.authorId;

    const hasImages = thread.imageUrls && thread.imageUrls.length > 0;
    const maxLines = hasImages ? 5 : 15;

    return (
        <Pressable onPress={handlePress} disabled={!isClickable || isNavigating}>
            <View style={styles.postContainer}>
                <View style={styles.authorContainer}>
                    {thread.author.photoURL ? (
                        <Image source={{ uri: thread.author.photoURL }} style={styles.authorImage} />
                    ) : (
                        <View style={styles.authorImagePlaceholder}>
                            <Text style={styles.authorImagePlaceholderText}>
                                {thread.author.displayName?.charAt(0).toUpperCase()}
                            </Text>
                        </View>
                    )}
                    <View style={styles.authorInfo}>
                        <View style={styles.authorMetaContainer}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <Text style={styles.authorName}>{thread.author.displayName}</Text>
                                <TimeStamp createdAt={thread.createdAt} />
                            </View>
                            <TouchableOpacity ref={ellipsisRef} onPress={handleOptionsPress}>
                                <Ellipsis size={20} color={theme.colors.Background.background700} />
                            </TouchableOpacity>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={styles.authorDetails}>{thread.author.userType}</Text>
                            {thread.author.condition && (
                                <>
                                    <Text style={[styles.authorDetails, styles.authorDetailsDot]}>•</Text>
                                    <Text style={styles.authorDetails}>{thread.author.condition}</Text>
                                </>
                            )}
                        </View>
                    </View>
                </View>
                <Text style={styles.postTitle}>{thread.title}</Text>
                <Text
                    style={styles.postContent}
                    numberOfLines={truncateContent ? maxLines : undefined}
                >
                    {thread.content}
                </Text>
                
                {/* Image Display */}
                {thread.imageUrls && thread.imageUrls.length > 0 && (
                    <View style={styles.imageContainer}>
                        {thread.imageUrls.length === 1 ? (
                            <TouchableOpacity
                                style={styles.singleImage}
                                onPress={() => handleImagePress(thread.imageUrls[0], 0)}
                            >
                                <Image
                                    source={{ uri: thread.imageUrls[0] }}
                                    style={styles.threadImageContent}
                                    resizeMode="cover"
                                />
                            </TouchableOpacity>
                        ) : (
                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                <View style={styles.imageRow}>
                                    {thread.imageUrls.map((imageUrl: string, index: number) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.multipleImages}
                                            onPress={() => handleImagePress(imageUrl, index)}
                                        >
                                            <Image
                                                source={{ uri: imageUrl }}
                                                style={styles.threadImageContent}
                                                resizeMode="cover"
                                            />
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            </ScrollView>
                        )}
                    </View>
                )}
                
                <View style={styles.actionBar}>
                    <View onStartShouldSetResponder={() => true}>
                        <Reactions type="thread" thread={thread} />
                    </View>
                    <CommentCounter count={thread.commentCount} />
                </View>
            </View>
            <ThreadOptionsMenu
                isVisible={menuVisible}
                onClose={() => setMenuVisible(false)}
                isAuthor={isAuthor}
                position={menuPosition}
            />
            
            {/* Image Focus Modal */}
            <Modal
                visible={imageModalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={handleCloseImageModal}
            >
                <View style={styles.modalContainer}>
                    <TouchableOpacity
                        style={styles.modalCloseButton}
                        onPress={handleCloseImageModal}
                    >
                        <X size={24} color={theme.colors.Background.background0} />
                    </TouchableOpacity>
                    
                    {thread?.imageUrls && thread.imageUrls.length > 1 && (
                        <View style={styles.imageIndicator}>
                            <Text style={styles.imageIndicatorText}>
                                {selectedImageIndex + 1} / {thread.imageUrls.length}
                            </Text>
                        </View>
                    )}
                    
                    {thread?.imageUrls && (
                        <ScrollView
                            horizontal
                            pagingEnabled
                            showsHorizontalScrollIndicator={false}
                            style={styles.modalScrollView}
                            onScroll={handleImageScroll}
                            scrollEventThrottle={16}
                            contentOffset={{ x: selectedImageIndex * screenWidth, y: 0 }}
                        >
                            {thread.imageUrls.map((imageUrl: string, index: number) => (
                                <View key={index} style={[styles.modalImageContainer, { width: screenWidth }]}>
                                    <Image
                                        source={{ uri: imageUrl }}
                                        style={styles.modalImage}
                                        resizeMode="contain"
                                    />
                                </View>
                            ))}
                        </ScrollView>
                    )}
                </View>
            </Modal>
        </Pressable>
    );
}

export default React.memo(ThreadCard)