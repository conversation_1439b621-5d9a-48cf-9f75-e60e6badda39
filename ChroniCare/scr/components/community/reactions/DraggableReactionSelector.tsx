import React, { useMemo } from 'react';
import { View, Text, StyleSheet, Dimensions, Modal } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { iconMapping } from '@/scr/hooks/useReactionDisplay';
import ReactionIcon from './ReactionIcon';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

const { width: screenWidth } = Dimensions.get('window');
export const REACTION_SIZE = 50;
export const REACTION_SPACING =45;
export const SELECTOR_HEIGHT = 50;
const REACTION_NAMES: Record<ReactionType, string> = {
  love: 'Love',
  withYou: 'With You',
  funny: 'Funny',
  insightful: 'Insightful',
  poop: 'Poop',
};

interface DraggableReactionSelectorProps {
  visible: boolean;
  onReactionSelect: (reaction: ReactionType) => void;
  onDismiss: () => void;
  triggerPosition: { x: number; y: number; width: number; height: number };
  myReaction: ReactionType | null;
  hoveredReaction: ReactionType | null;
}

const DraggableReactionSelector: React.FC<DraggableReactionSelectorProps> = ({
  visible,
  onReactionSelect: _onReactionSelect,
  onDismiss: _onDismiss,
  triggerPosition,
  myReaction,
  hoveredReaction,
}) => {
  const { theme } = useTheme();
  const [isRendered, setIsRendered] = React.useState(false);
  
  // Get reactions list - memoize to keep reference stable
  const reactions = useMemo(() => Object.keys(iconMapping) as ReactionType[], []);
  
  // Animation values
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const labelOpacity = useSharedValue(0);
  const labelTranslateY = useSharedValue(10);
  
  // Create individual shared values for each reaction
  const love_anim = useSharedValue(0);
  const love_scale = useSharedValue(1);
  const withYou_anim = useSharedValue(0);
  const withYou_scale = useSharedValue(1);
  const funny_anim = useSharedValue(0);
  const funny_scale = useSharedValue(1);
  const insightful_anim = useSharedValue(0);
  const insightful_scale = useSharedValue(1);
  const poop_anim = useSharedValue(0);
  const poop_scale = useSharedValue(1);
  
  // Create animation objects
  const reactionAnimations = useMemo(() => ({
    love: love_anim,
    withYou: withYou_anim,
    funny: funny_anim,
    insightful: insightful_anim,
    poop: poop_anim,
  }), [love_anim, withYou_anim, funny_anim, insightful_anim, poop_anim]);
  
  const reactionScales = useMemo(() => ({
    love: love_scale,
    withYou: withYou_scale,
    funny: funny_scale,
    insightful: insightful_scale,
    poop: poop_scale,
  }), [love_scale, withYou_scale, funny_scale, insightful_scale, poop_scale]);

  React.useEffect(() => {
    if (visible) {
      setIsRendered(true);
      // Show selector with spring animation
      opacity.value = withTiming(1, { duration: 200 });
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      
      // Staggered animation for reactions appearing left to right
      reactions.forEach((reaction, index) => {
        reactionAnimations[reaction].value = withDelay(
          index * 50,
          withSpring(1, { damping: 20, stiffness: 400 })
        );
      });
    } else {
      // Hide selector with a smooth, staggered spring animation
      const reversedReactions = [...reactions].reverse();

      // Animate each reaction icon out with a spring and a delay
      reversedReactions.forEach((reaction, index) => {
        reactionAnimations[reaction].value = withDelay(
          index * 30, // Stagger from right to left
          withSpring(0, {
            damping: 20,
            stiffness: 300,
            mass: 0.5,
          })
        );
        // Ensure icon scales back to normal if it was hovered
        reactionScales[reaction].value = withSpring(1);
      });

      // Animate the container out after the icons start disappearing
      const totalDelay = reactions.length * 30 + 50;
      opacity.value = withDelay(
        totalDelay,
        withTiming(0, { duration: 150 }, (finished) => {
          if (finished) {
            runOnJS(setIsRendered)(false);
          }
        })
      );
      scale.value = withDelay(
        totalDelay,
        withSpring(0.8, { damping: 15, stiffness: 300 })
      );
      
      // Hide label
      labelOpacity.value = withTiming(0, { duration: 100 });
    }
  }, [visible, reactions, reactionAnimations, reactionScales, opacity, scale, labelOpacity]);

  // Handle hover effects when hoveredReaction changes
  React.useEffect(() => {
    if (hoveredReaction) {
      // Reset all reactions first
      reactions.forEach((reaction) => {
        if (reaction !== hoveredReaction) {
          reactionScales[reaction].value = withSpring(1, { damping: 15 });
        }
      });
      
      // Scale up hovered reaction
      reactionScales[hoveredReaction].value = withSpring(1.3, { damping: 15 });
      
      // Show label
      labelOpacity.value = withTiming(1, { duration: 200 });
      labelTranslateY.value = withSpring(0, { damping: 15 });
    } else {
      // Reset all reactions when no hover
      reactions.forEach((reaction) => {
        reactionScales[reaction].value = withSpring(1, { damping: 15 });
      });
      
      // Hide label
      labelOpacity.value = withTiming(0, { duration: 150 });
      labelTranslateY.value = withTiming(10, { duration: 150 });
    }
  }, [hoveredReaction, reactionScales, labelOpacity, labelTranslateY, reactions]);

  const calculateSelectorPosition = () => {
    const selectorWidth = reactions.length * REACTION_SPACING + 30; // Add padding
    const centerX = triggerPosition.x + (triggerPosition.width / 2);
    let x = centerX - selectorWidth / 2;
    
    // Keep selector within screen bounds
    if (x < 20) x = 20;
    if (x + selectorWidth > screenWidth - 20) x = screenWidth - selectorWidth - 20;
    
    // Position above the trigger button
    const y = triggerPosition.y - SELECTOR_HEIGHT - 10;
    
    return { x, y, width: selectorWidth };
  };

  const selectorPosition = calculateSelectorPosition();


  const containerStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const labelStyle = useAnimatedStyle(() => ({
    opacity: labelOpacity.value,
    transform: [{ translateY: labelTranslateY.value }],
  }));

  // Create individual animated styles for each reaction
  const loveStyle = useAnimatedStyle(() => ({
    opacity: love_anim.value,
    transform: [
      {
        scale: interpolate(love_anim.value, [0, 1], [0.5, love_scale.value]),
      },
      {
        translateY: interpolate(love_anim.value, [0, 1], [20, 0]),
      },
    ],
  }));
  
  const withYouStyle = useAnimatedStyle(() => ({
    opacity: withYou_anim.value,
    transform: [
      {
        scale: interpolate(withYou_anim.value, [0, 1], [0.5, withYou_scale.value]),
      },
      {
        translateY: interpolate(withYou_anim.value, [0, 1], [20, 0]),
      },
    ],
  }));
  
  const funnyStyle = useAnimatedStyle(() => ({
    opacity: funny_anim.value,
    transform: [
      {
        scale: interpolate(funny_anim.value, [0, 1], [0.5, funny_scale.value]),
      },
      {
        translateY: interpolate(funny_anim.value, [0, 1], [20, 0]),
      },
    ],
  }));
  
  const insightfulStyle = useAnimatedStyle(() => ({
    opacity: insightful_anim.value,
    transform: [
      {
        scale: interpolate(insightful_anim.value, [0, 1], [0.5, insightful_scale.value]),
      },
      {
        translateY: interpolate(insightful_anim.value, [0, 1], [20, 0]),
      },
    ],
  }));
  
  const poopStyle = useAnimatedStyle(() => ({
    opacity: poop_anim.value,
    transform: [
      {
        scale: interpolate(poop_anim.value, [0, 1], [0.5, poop_scale.value]),
      },
      {
        translateY: interpolate(poop_anim.value, [0, 1], [20, 0]),
      },
    ],
  }));
  
  const reactionStyles = useMemo(() => ({
    love: loveStyle,
    withYou: withYouStyle,
    funny: funnyStyle,
    insightful: insightfulStyle,
    poop: poopStyle,
  }), [loveStyle, withYouStyle, funnyStyle, insightfulStyle, poopStyle]);

  if (!isRendered) return null;

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    container: {
      position: 'absolute',
      left: selectorPosition.x,
      top: selectorPosition.y,
      width: selectorPosition.width,
      height: SELECTOR_HEIGHT,
      flexDirection: 'row',
      backgroundColor: theme.colors.Background.background100,
      borderRadius: 40,
      paddingHorizontal: 0,
      alignItems: 'center',
      justifyContent: 'space-around',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 10,
    },
    reactionContainer: {
      width: REACTION_SIZE,
      height: REACTION_SIZE,
      justifyContent: 'center',
      alignItems: 'center',
    },
    labelContainer: {
      position: 'absolute',
      top: -35,
      alignSelf: 'center',
      backgroundColor: theme.colors.Background.background800,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 15,
      flexShrink: 0,
      flexWrap: 'nowrap',
      width: 'auto',
      minWidth: 80,
    },
    labelText: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text100,
      textAlign: 'center',
      flexShrink: 0,
    },
  });

  return (
    <Modal
      visible={isRendered}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalOverlay}>
        <Animated.View style={[styles.container, containerStyle]}>
          {reactions.map((reaction) => (
            <Animated.View
              key={reaction}
              style={[styles.reactionContainer, reactionStyles[reaction]]}
            >
              <ReactionIcon
                reactionType={reaction}
                isSelected={true}
                size={28}
              />
              {hoveredReaction === reaction && (
                <Animated.View style={[styles.labelContainer, labelStyle]}>
                  <Text style={styles.labelText}>
                    {REACTION_NAMES[reaction]}
                  </Text>
                </Animated.View>
              )}
            </Animated.View>
          ))}
        </Animated.View>
      </View>
    </Modal>
  );
};

export default React.memo(DraggableReactionSelector);