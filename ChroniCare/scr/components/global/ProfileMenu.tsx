import { View, Text, Image, StyleSheet, Modal, TouchableOpacity, Pressable, Alert } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '../../context/themeContext';
import { useAuth } from '../../context/authContext';
import { LogOut, Mail, Camera } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useProfilePicture } from '../../hooks/useProfilePicture';

interface ProfileMenuProps {
    isVisible: boolean;
    onClose: () => void;
}

const ProfileMenu = ({ isVisible, onClose }: ProfileMenuProps) => {
    const { theme } = useTheme();
    const { signOut } = useAuth();
    const { user, uploadProfilePicture, isUploading } = useProfilePicture();

    const profileImageSource = user?.photoURL
        ? { uri: user.photoURL }
        : require('../../../assets/images/placeholderProfileImage.png');

    const handleUploadPicture = async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
            alert('Sorry, we need camera roll permissions to make this work!');
            return;
        }
        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            allowsEditing: true, 
            aspect: [1, 1],
            quality: 0.5,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
            const imageUri = result.assets[0].uri;
            const mimeType = result.assets[0].mimeType;

            if (!mimeType) {
                alert('Could not determine file type. Please select another image.');
                return;
            }

            try {
                await uploadProfilePicture(imageUri, mimeType);
                onClose(); // Close modal on success
            } catch (error) {
                console.error('Error uploading profile picture:', error);
                alert('Failed to upload profile picture. Please try again.');
            }
        }
    }

    const styles = useMemo(() => StyleSheet.create({
        outerContainer: {
            flex: 1,
        },
        overlay: {
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
        },
        pressableContainer: {
            flex: 1,
            justifyContent: 'flex-start',
            alignItems: 'flex-end',
            paddingTop: theme.spacing.spacing.s24,
            paddingRight: theme.spacing.spacing.s4,
        },
        menuContainer: {
            borderRadius: theme.spacing.borderRadius.xl,
            width: 250,
            padding: theme.spacing.spacing.s4,
            elevation: 5,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
        },
        userInfoContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        profileImage: {
            width: 40,
            height: 40,
            borderRadius: 20,
            marginRight: theme.spacing.spacing.s3,
        },
        userName: {
            ...theme.textVariants.text('md', 'semibold'),
            color: theme.colors.Text.text900,
        },
        separator: {
            height: theme.spacing.borderWidth.s1,
            backgroundColor: theme.colors.Background.background200,
            marginVertical: theme.spacing.spacing.s3,
        },
        menuItem: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: theme.spacing.spacing.s3,
            paddingHorizontal: 0,
        },
        menuItemText: {
            marginLeft: theme.spacing.spacing.s3,
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        }
    }), [theme]);

    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={isVisible}
            onRequestClose={onClose}
        >
            <Pressable style={styles.outerContainer} onPress={onClose}>
                <View style={styles.overlay}>
                    <Pressable style={styles.pressableContainer} onPress={onClose}>
                        <Pressable style={[styles.menuContainer, { backgroundColor: theme.colors.Background.background0 }]}
                            onPress={() => {}}
                        >
                            <View style={styles.userInfoContainer}>
                                <Image source={profileImageSource} style={styles.profileImage} />
                                <Text style={styles.userName}>{user?.firstName} {user?.lastName}</Text>
                            </View>
                            <View style={styles.separator} />
                            <TouchableOpacity style={styles.menuItem} onPress={handleUploadPicture} disabled={isUploading}>
                                <Camera color={theme.colors.Text.text900} size={24} />
                                <Text style={styles.menuItemText}>{isUploading ? 'Uploading...' : 'Change profile picture'}</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.menuItem} onPress={signOut}>
                                <LogOut color={theme.colors.Text.text900} size={24} />
                                <Text style={styles.menuItemText}>Log out</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.menuItem} onPress={() => { Alert.alert('Contact Us', 'Do not hesitate to contact <NAME_EMAIL>') }}>
                                <Mail color={theme.colors.Text.text900} size={24} />
                                <Text style={styles.menuItemText}>Contact us</Text>
                            </TouchableOpacity>
                        </Pressable>
                    </Pressable>
                </View>
            </Pressable>
        </Modal>
    );
}

export default ProfileMenu;