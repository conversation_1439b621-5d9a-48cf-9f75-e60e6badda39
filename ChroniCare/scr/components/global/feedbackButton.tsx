import React from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import { MessageSquarePlus } from 'lucide-react-native';
import { useTheme } from '../../context/themeContext';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

interface FeedbackButtonProps {
  onPress: () => void;
}

const BUTTON_SIZE = 50;

const FeedbackButton: React.FC<FeedbackButtonProps> = ({ onPress }) => {
  const theme = useTheme();
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();

  const translateX = useSharedValue(screenWidth - BUTTON_SIZE - 40);
  const translateY = useSharedValue(screenHeight - BUTTON_SIZE - 150);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);

  const panGesture = Gesture.Pan()
    .onBegin(() => {
      savedTranslateX.value = translateX.value;
      savedTranslateY.value = translateY.value;
    })
    .onUpdate((event) => {
      let newX = savedTranslateX.value + event.translationX;
      let newY = savedTranslateY.value + event.translationY;

      const maxTranslateX = screenWidth - BUTTON_SIZE;
      const maxTranslateY = screenHeight - BUTTON_SIZE;

      newX = Math.max(0, Math.min(newX, maxTranslateX));
      newY = Math.max(0, Math.min(newY, maxTranslateY));

      translateX.value = newX;
      translateY.value = newY;
    });

  const tapGesture = Gesture.Tap().onEnd(() => {
    runOnJS(onPress)();
  });
  
  const composedGesture = Gesture.Exclusive(panGesture, tapGesture);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View
        style={[
          styles.button,
          { backgroundColor: theme.theme.colors.Primary.primary500 },
          animatedStyle
        ]}
      >
        <MessageSquarePlus color={theme.theme.colors.Text.text0} size={25} />
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    width: BUTTON_SIZE,
    height: BUTTON_SIZE,
    borderRadius: BUTTON_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
});

export default FeedbackButton;
