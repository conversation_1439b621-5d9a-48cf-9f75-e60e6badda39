import React, { useMemo } from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '@/scr/context/themeContext';

interface AvatarPlaceholderProps {
    name?: string;
    size?: number;
    style?: ViewStyle;
    textStyle?: TextStyle;
}

const AvatarPlaceholder: React.FC<AvatarPlaceholderProps> = ({ 
    name, 
    size = 32, 
    style, 
    textStyle 
}) => {
    const { theme } = useTheme();

    // Function to get a consistent color based on the first letter of the name
    const getAvatarColor = (name?: string): string => {
        if (!name || name.length === 0) {
            return theme.colors.Primary.primary500; // Default color
        }

        const firstLetter = name.charAt(0).toLowerCase();
        const colorPalettes = [
            '#fad1df',
          
        ];

        // Use character code to get a consistent index
        const charCode = firstLetter.charCodeAt(0);
        const colorIndex = charCode % colorPalettes.length;
        
        return colorPalettes[colorIndex];
    };

    const backgroundColor = getAvatarColor(name);
    const firstLetter = name?.charAt(0).toUpperCase() || '?';

    const styles = useMemo(() => StyleSheet.create({
        container: {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        },
        text: {
            ...theme.textVariants.text('sm', 'bold'),
            color: '#FFFFFF',
            fontSize: size * 0.4, // Scale font size based on avatar size
        },
    }), [theme, size, backgroundColor]);

    return (
        <View style={[styles.container, style]}>
            <Text style={[styles.text, textStyle]}>
                {firstLetter}
            </Text>
        </View>
    );
};

export default AvatarPlaceholder;
