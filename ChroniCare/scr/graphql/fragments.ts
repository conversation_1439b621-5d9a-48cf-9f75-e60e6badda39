import { gql } from '@apollo/client';

export const THREAD_FRAGMENT = gql`
  fragment ThreadFragment on Thread {
    _id
    author {
      authorId
      displayName
      condition
      userType
      photoURL
    }
    title
    content
    imageUrls
    createdAt
    commentCount
    communityId
    reactionCounts {
      love
      withYou
      funny
      insightful
      poop
    }
    myReaction
  }
`;

export interface Author {
  authorId: string;
  displayName: string;
  condition?: string;
  userType: string;
  photoURL: string;
  commentCount: number;
  reactionCounts: ReactionCount;
  myReaction: string | null;
}

export interface ReactionCount {
  love: number;
  withYou: number;
  funny: number;
  insightful: number;
  poop: number;
}

export interface Thread {
  _id: string;
  author: Author;
  title: string;
  content: string;
  createdAt: string;
  commentCount: number;
  communityId: string;
  reactionCounts: ReactionCount;
  myReaction: string | null;
} 