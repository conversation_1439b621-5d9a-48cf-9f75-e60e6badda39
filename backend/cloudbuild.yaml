steps:
  # 1. Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'build',
        '-t',
        '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPO_NAME}/${_SERVICE_NAME}:${SHORT_SHA}',
        '.',
      ]

  # 2. Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPO_NAME}/${_SERVICE_NAME}:${SHORT_SHA}',
      ]

  # 3. Run database migrations before deployment
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'run'
      - '--rm'
      - '--env'
      - 'DATABASE_URL=$$DATABASE_URL'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPO_NAME}/${_SERVICE_NAME}:${SHORT_SHA}'
      - 'npx'
      - 'prisma'
      - 'migrate'
      - 'deploy'
    secretEnv: ['DATABASE_URL']

  # 4. Deploy the new image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPO_NAME}/${_SERVICE_NAME}:${SHORT_SHA}'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      # Mount secrets as environment variables
      - '--update-secrets=DATABASE_URL=DATABASE_URL:latest'
      - '--update-secrets=MONGO_DATABASE_URL=MONGO_DATABASE_URL:latest'
      - '--update-secrets=GOOGLE_CLOUD_PROJECT_ID=GOOGLE_CLOUD_PROJECT_ID:latest'
      - '--update-secrets=GOOGLE_CLOUD_STORAGE_BUCKET=GOOGLE_CLOUD_STORAGE_BUCKET:latest'
      - '--update-secrets=SERVICE_ACCOUNT_PATH=SERVICE_ACCOUNT_PATH:latest'
      - '--update-secrets=GOOGLE_CLOUD_KEY_FILE=GOOGLE_CLOUD_KEY_FILE:latest'
      # Set non-secret environment variables
      - '--set-env-vars=NODE_ENV=production'
      - '--set-env-vars=PORT=3000'
      - '--set-env-vars=GOOGLE_APPLICATION_CREDENTIALS=/usr/src/app/gcp-service-account.json'
      # Resource limits for production
      - '--memory=2Gi'
      - '--cpu=1'
      - '--concurrency=100'
      # Keep at least one instance warm to avoid cold starts
      - '--min-instances=1'
      - '--max-instances=10'
      # Security and networking
      - '--allow-unauthenticated'
      # Health check configuration
      - '--timeout=300'
      # Service account for Firebase and GCP services
      - '--service-account=${_SERVICE_ACCOUNT_EMAIL}'

# Store the built image in Artifact Registry
images:
  - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPO_NAME}/${_SERVICE_NAME}:${SHORT_SHA}'

# Define substitutions for different environments
substitutions:
  _SERVICE_NAME: 'chronicare'
  _REPO_NAME: 'chronicare-repo'
  _REGION: 'europe-north2'
  _SERVICE_ACCOUNT_EMAIL: '<EMAIL>'

# Cloud Build configuration
options:
  # Use a more powerful machine for faster builds
  machineType: 'E2_HIGHCPU_8'
  # Enable logging
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build process
timeout: '1200s'

# Available secrets for build steps
availableSecrets:
  secretManager:
    - versionName: projects/${PROJECT_ID}/secrets/DATABASE_URL/versions/latest
      env: 'DATABASE_URL'
