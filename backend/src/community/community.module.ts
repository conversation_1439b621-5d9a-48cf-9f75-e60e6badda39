import { Module } from '@nestjs/common';
import { ThreadResolver, CommentResolver } from './community.resolver';
import { MongoModule } from '../mongo/mongo.module';
import { ThreadsModule } from './threads/threads.module';
import { CommentsModule } from './comments/comments.module';
import { ReactionsModule } from './reactions/reactions.module';

@Module({
  imports: [MongoModule, ThreadsModule, CommentsModule, ReactionsModule],
  providers: [ThreadResolver, CommentResolver],
  exports: [ThreadsModule, CommentsModule, ReactionsModule],
})
export class CommunityModule {}
