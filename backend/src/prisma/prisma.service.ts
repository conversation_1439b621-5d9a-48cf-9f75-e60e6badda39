import { Injectable, OnModuleInit, OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common';
import { PrismaClient, Prisma } from '@prisma/client';
import { withAccelerate } from '@prisma/extension-accelerate';

// This type will be used for the extended client
export type ExtendedPrismaClient = ReturnType<
  typeof createExtendedPrismaClient
>;

// This function creates the extended client instance
function createExtendedPrismaClient() {
  return new PrismaClient().$extends(withAccelerate());
}

@Injectable()
// We change from `extends PrismaClient` to just implementing the lifecycle hooks.
// This is because the `extends` pattern is too rigid for use with Prisma's client extensions.
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  // The client is now of the exported type
  readonly client: ExtendedPrismaClient = createExtendedPrismaClient();

  constructor() {
    // The client is initialized via the exported function
  }

  async onModuleInit() {
    // With Accelerate, explicit connection management is handled for you,
    // but it's safe to keep these hooks.
    await this.client.$connect();
    console.log('Prisma service initialized and connected via Accelerate.');
  }

  async onModuleDestroy() {
    await this.client.$disconnect();
  }
} 