import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  PrismaService,
  ExtendedPrismaClient,
} from '../prisma/prisma.service';
import { CompleteOnboardingInput } from './dto/complete-onboarding.input';
import { users, UserRole, Prisma } from '@prisma/client';
import { UsersService } from '../users/users.service';

// Define the shape of the parsed JSON objects
interface DiseaseUserType {
  role: 'patient' | 'caregiver';
  diagnosisStatus?: 'diagnosed' | 'not-diagnosed';
  diagnosisDate?: Date;
}

interface MedicationItem {
    id: string;
    label: string;
}

interface MedicationEntry {
    medication: MedicationItem;
    dosage?: string;
    frequency?: string;
    notes?: string;
    startDate?: Date;
    isCurrent: boolean;
}

interface MedicalDeviceItem {
    id: string;
    label: string;
}

interface MedicalDeviceEntry {
    device: MedicalDeviceItem;
}


@Injectable()
export class OnboardingService {
  private readonly logger = new Logger(OnboardingService.name);

  constructor(
    private prisma: PrismaService,
    private usersService: UsersService,
  ) {}

  private mapToUserRole(userType: DiseaseUserType): UserRole {
    if (userType.role === 'caregiver') {
      return UserRole.CAREGIVER;
    }
    if (userType.role === 'patient') { 
      if (userType.diagnosisStatus === 'diagnosed') {
        return UserRole.DIAGNOSED;
      }
      return UserRole.UNDIAGNOSED;
    }
    // Default fallback
    return UserRole.UNDIAGNOSED;
  }

  private async addUserToCommunities(
    tx: Prisma.TransactionClient,
    userHealthId: string,
    processedDiseases: Prisma.DiseaseGetPayload<{ include: { parent: true } }>[],
  ): Promise<void> {
    this.logger.log(
      `Starting community membership process for user ${userHealthId} with ${processedDiseases.length} processed diseases`,
    );

    // Step 1: For each selected disease, find its parent disease (or use itself if no parent)
    const communityTargetDiseaseIds = processedDiseases.map((dbDisease) => {
      if (dbDisease.parentId) {
        this.logger.log(
          `Disease ${dbDisease.name} (${dbDisease.id}) has parent disease ID: ${dbDisease.parentId}`,
        );
        return dbDisease.parentId;
      } else {
        this.logger.log(
          `Disease ${dbDisease.name} (${dbDisease.id}) has no parent, using itself for community lookup`,
        );
        return dbDisease.id;
      }
    });

    // Step 2: Get unique disease IDs to avoid duplicate community memberships
    const uniqueCommunityTargetDiseaseIds = [...new Set(communityTargetDiseaseIds)];
    this.logger.log(
      `After deduplication: ${uniqueCommunityTargetDiseaseIds.length} unique target diseases for communities`,
    );

    // Step 3: For each unique target disease, find its communities and add user
    let totalCommunitiesJoined = 0;

    for (const diseaseId of uniqueCommunityTargetDiseaseIds) {
      this.logger.log(`Finding communities for disease ID: ${diseaseId}`);

      const communities = await tx.community.findMany({
        where: {
          diseaseGroupId: diseaseId,
          isActive: true,
        },
      });

      this.logger.log(
        `Found ${communities.length} active communities for disease ID: ${diseaseId}`,
      );

      for (const community of communities) {
        this.logger.log(
          `Checking membership for community: ${community.name} (ID: ${community.id})`,
        );

        // Check if membership already exists to avoid duplicates
        const existingMembership = await tx.communityMembership.findUnique({
          where: {
            user_healthID_communityId: {
              user_healthID: userHealthId,
              communityId: community.id,
            },
          },
        });

        if (existingMembership) {
          this.logger.log(
            `User ${userHealthId} is already a member of community: ${community.name}`,
          );
        } else {
          await tx.communityMembership.create({
            data: {
              user_healthID: userHealthId,
              communityId: community.id,
              memberRole: 'MEMBER',
              joinedAt: new Date(),
            },
          });
          totalCommunitiesJoined++;
          this.logger.log(
            `Successfully added user ${userHealthId} to community: ${community.name}`,
          );
        }
      }
    }

    this.logger.log(
      `Community membership process completed. User ${userHealthId} joined ${totalCommunitiesJoined} new communities`,
    );
  }
  
  async completeOnboarding(
    userId: string,
    data: CompleteOnboardingInput,
  ): Promise<users> {
    this.logger.log(`Starting onboarding completion for user ${userId}`);

    const {
      consent,
      personalInfo,
      profilePicture,
      diseases,
      userTypes,
      medications,
      medicalDevices = null,
    } = data;
      
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    if (!pseudonym) {
        throw new NotFoundException(`Pseudonym not found for user ${userId}`);
    }
    const userHealthId = pseudonym.pseudonymId;

    // Parse JSON string data from input
    const diseaseUserTypes: Record<string, DiseaseUserType> = JSON.parse(userTypes.diseaseUserTypes);
    const diseaseRelatedMedications: Record<string, MedicationEntry[]> = JSON.parse(medications.diseaseRelatedMedications);
    const diseaseRelatedMedicalDevices: Record<string, MedicalDeviceEntry[]> = medicalDevices ? JSON.parse(medicalDevices.diseaseRelatedMedicalDevices) : {};

    return this.prisma.client.$transaction(async (tx) => {
      // Cast the extended transaction client to the base Prisma.TransactionClient type.
      // This is a safe workaround for the complex type issues caused by client extensions.
      const transactionClient = tx as unknown as Prisma.TransactionClient;

      // 1. Update User record with personal info and profile picture
      const updatedUser = await transactionClient.users.update({
        where: { id: userId },
        data: {
          firstName: personalInfo.firstName,
          lastName: personalInfo.lastName,
          displayName: `${personalInfo.firstName} ${personalInfo.lastName}`,
          birthdate: personalInfo.birthdate,
          gender: personalInfo.gender,
          countryCode: personalInfo.countryCode,
          countryName: personalInfo.countryName,
          photoURL: profilePicture.imageUri,
          onboardingCompleted: true,
          updatedAt: new Date(),
        },
      });
      this.logger.log(`Updated user info for ${userId}`);

      // 2. Update UserConsent records
      const version = process.env.CONSENT_VERSION || 'v1.0';
      
      // Create consent records for each type
      // Terms of Service is handled at signup, not during onboarding
      const consentTypes = [
        { type: 'PRIVACY_POLICY' as const, granted: consent.dataPrivacy },
        { type: 'DATA_SHARING' as const, granted: consent.dataSharing },
        { type: 'MARKETING' as const, granted: consent.marketing },
      ];

      for (const { type, granted } of consentTypes) {
        await transactionClient.user_consents.create({
          data: {
            userId,
            type,
            version,
            granted,
            timestamp: new Date(),
          },
        });
      }
      this.logger.log(`Updated consent records for ${userId}`);
      
      const diseaseToProfileIdMap = new Map<string, string>();
      const processedDbDiseases: Prisma.DiseaseGetPayload<{
        include: { parent: true };
      }>[] = [];

      // 3. Process diseases and user disease roles
      this.logger.log(
        `Processing ${diseases.selectedDiseases.length} diseases for user ${userId}`,
      );
      for (const disease of diseases.selectedDiseases) {
        // Find the disease by its ICD code
        const dbDisease = await transactionClient.disease.findFirst({
          where: { icdCode: disease.icdCode },
          include: { parent: true }, // Include parent for community logic
        });

        if (!dbDisease) {
          // If disease is not found, it's a critical error as per requirements
          this.logger.error(
            `Disease with ICD code ${disease.icdCode} not found in database.`,
          );
          throw new NotFoundException(
            `Disease with ICD code ${disease.icdCode} not found.`,
          );
        }
        this.logger.log(
          `Found disease: ${dbDisease.name} (ID: ${dbDisease.id}) for ICD code ${disease.icdCode}`,
        );
        processedDbDiseases.push(dbDisease);

        const userType = diseaseUserTypes[disease.id];
        if (userType) {
          const userRole = this.mapToUserRole(userType);
          const profile = await transactionClient.userDiseaseProfile.create({
            data: {
              user_healthID: userHealthId,
              diseaseId: dbDisease.id,
              userRole: userRole,
              ...(userType.diagnosisDate && {
                diagnosisDate: new Date(userType.diagnosisDate),
              }),
            },
          });
          diseaseToProfileIdMap.set(disease.id, profile.id);
          this.logger.log(
            `Created UserDiseaseProfile for disease ${dbDisease.name} for user ${userId}`,
          );
        }
      }
      this.logger.log(
        `Processed ${diseases.selectedDiseases.length} diseases for user ${userId}`,
      );

      // 3a. Add user to communities based on selected diseases
      await this.addUserToCommunities(transactionClient, userHealthId, processedDbDiseases);

      // 4. Process disease-related medications
      for (const [diseaseId, medicationEntries] of Object.entries(
        diseaseRelatedMedications,
      )) {
          const userDiseaseProfileId = diseaseToProfileIdMap.get(diseaseId);

          if (userDiseaseProfileId) {
              for (const entry of medicationEntries) {
                  const dbMedication = await transactionClient.medicationMaster.upsert({
                      where: { genericName: entry.medication.label },
                      update: {},
                      create: {
                          id: entry.medication.id,
                          genericName: entry.medication.label,
                          brandNames: [],
                      },
                  });

                  await transactionClient.userMedication.create({
                      data: {
                          user_healthID: userHealthId,
                          medicationMasterId: dbMedication.id,
                          userDiseaseProfileId: userDiseaseProfileId, // Link to the specific user disease
                          dosage: entry.dosage,
                          frequency: entry.frequency,
                          notes: entry.notes,
                          startDate: entry.startDate,
                          isCurrent: entry.isCurrent,
                      },
                  });
              }
          }
      }
      this.logger.log(`Processed disease-related medications for user ${userId}`);
      
      // 5. Process unrelated medications
      for (const entry of medications.unrelatedMedications) {
        const dbMedication = await transactionClient.medicationMaster.upsert({
            where: { genericName: entry.medication.label },
            update: {},
            create: {
                id: entry.medication.id,
                genericName: entry.medication.label,
                brandNames: [],
            },
        });

        await transactionClient.userMedication.create({
            data: {
                user_healthID: userHealthId,
                medicationMasterId: dbMedication.id,
                // userDiseaseProfileId is null for unrelated medications
                dosage: entry.dosage,
                frequency: entry.frequency,
                notes: entry.notes,
                startDate: entry.startDate,
                isCurrent: entry.isCurrent,
            },
        });
      }
      this.logger.log(`Processed unrelated medications for user ${userId}`);

      // 6. Process disease-related medical devices
      for (const [diseaseId, deviceEntries] of Object.entries(
        diseaseRelatedMedicalDevices,
      )) {
          const userDiseaseProfileId = diseaseToProfileIdMap.get(diseaseId);

          if (userDiseaseProfileId) {
              for (const entry of deviceEntries) {
                  const dbMedicalDevice = await transactionClient.medicalDevice.upsert({
                      where: { genericName: entry.device.label },
                      update: {},
                      create: {
                          id: entry.device.id,
                          genericName: entry.device.label,
                          brandNames: [],
                      },
                  });

                  await transactionClient.userMedicalDevice.create({
                      data: {
                          user_healthID: userHealthId,
                          medicalDeviceId: dbMedicalDevice.id,
                          isCurrent: true,
                      },
                  });
              }
          }
      }
      this.logger.log(`Processed disease-related medical devices for user ${userId}`);

      this.logger.log(`Onboarding successfully completed for user ${userId}`);
      return updatedUser;
    });
  }

  async updateProfilePicture(userId: string, photoURL: string): Promise<users> {
    this.logger.log(`Updating profile picture for user ${userId}`);
    
    return this.prisma.client.users.update({
      where: { id: userId },
      data: {
        photoURL,
        updatedAt: new Date(),
      },
    });
  }
}
