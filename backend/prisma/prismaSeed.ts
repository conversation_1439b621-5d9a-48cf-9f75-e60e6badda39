import { PrismaClient } from '@prisma/client';
import { medications as medicationsData } from '../src/data/medications';
import { medicalDevices as medicalDevicesData } from '../src/data/medicalDevices';

const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding public schema...`);

  // Check if IBD already exists
  let ibd = await prisma.disease.findUnique({
    where: { name: 'Inflammatory Bowel Disease' },
    include: { subDiseases: true },
  });

  if (!ibd) {
    ibd = await prisma.disease.create({
      data: {
        name: 'Inflammatory Bowel Disease',
        description:
          'A group of inflammatory conditions of the colon and small intestine.',
        isActive: true,
        displayName: 'IBD',
        icdCode: 'DD7Z',
        subDiseases: {
          create: [
            {
              name: "<PERSON><PERSON><PERSON>'s Disease",
              description:
                'A type of inflammatory bowel disease (IBD) that may affect any part of the gastrointestinal tract from mouth to anus.',
              isActive: true,
              icdCode: 'DD70',
              displayName: '<PERSON><PERSON><PERSON>\'s Disease',
            },
            {
              name: 'Ulcerative Colitis',
              description:
                'A type of inflammatory bowel disease (IBD) that affects the lining of the large intestine (colon) and rectum.',
              isActive: true,
              icdCode: 'DD71',
              displayName: 'UC',
            },
            {
              name: 'Inflammatory Bowel Syndrome',
              description:
                'A common disorder that affects the large intestine. Signs and symptoms include cramping, abdominal pain, bloating, gas, and diarrhea or constipation, or both.',
              isActive: true,
              icdCode: 'DD7Z',
              displayName: 'IBD',
            },
          ],
        },
      },
      include: {
        subDiseases: true,
      },
    });
    console.log(`Created disease 'Inflammatory bowel disease' with id: ${ibd.id}`);
    for (const subDisease of ibd.subDiseases) {
      console.log(
        `Created sub-disease '${subDisease.name}' with id: ${subDisease.id}`,
      );
    }
  } else {
    console.log(`Disease 'Inflammatory bowel disease' already exists with id: ${ibd.id}`);
  }

  // Check if IBD community already exists
  let ibdCommunity = await prisma.community.findUnique({
    where: { name: 'IBD Ambassadors' },
  });

  if (!ibdCommunity) {
    ibdCommunity = await prisma.community.create({
      data: {
        name: 'IBD Ambassadors',
        description: 'A community for people affected by IBD.',
        diseaseGroupId: ibd.id,
        isActive: true,
      },
    });
    console.log(
      `Created community 'Inflammatory bowel disease' with id: ${ibdCommunity.id}`,
    );
  } else {
    console.log(`Community 'Inflammatory bowel disease' already exists with id: ${ibdCommunity.id}`);
  }

  // Update disease with primary community if not already set
  if (!ibd.primaryCommunityId) {
    await prisma.disease.update({
      where: { id: ibd.id },
      data: { primaryCommunityId: ibdCommunity.id },
    });
    console.log(`Updated 'Inflammatory bowel disease' with primary community.`);
  } else {
    console.log(`Disease 'Inflammatory bowel disease' already has primary community set.`);
  }

  for (const med of medicationsData) {
    await prisma.medicationMaster.upsert({
      where: { id: med.id },
      update: {
        genericName: med.genericName,
        brandNames: med.brandNames,
        localizedNames: med.localizedNames || undefined,
        atcCode: med.atcCode,
        drugClass: med.drugClass,
        drugType: med.drugType,
      },
      create: {
        id: med.id,
        genericName: med.genericName,
        brandNames: med.brandNames,
        localizedNames: med.localizedNames || undefined,
        atcCode: med.atcCode,
        drugClass: med.drugClass,
        drugType: med.drugType,
      },
    });
  }
  console.log(`Seeded ${medicationsData.length} medications.`);

  // Seed medical devices
  for (const device of medicalDevicesData) {
    await prisma.medicalDevice.upsert({
      where: { id: device.id },
      update: {
        genericName: device.genericName,
        brandNames: device.brandNames,
        localizedNames: device.localizedNames || undefined,
        deviceClass: device.deviceClass,
        description: device.description,
      },
      create: {
        id: device.id,
        genericName: device.genericName,
        brandNames: device.brandNames,
        localizedNames: device.localizedNames || undefined,
        deviceClass: device.deviceClass,
        description: device.description,
      },
    });
  }
  console.log(`Seeded ${medicalDevicesData.length} medical devices.`);

  const symptoms = [
    {
      name: 'Diarrhea',
      description: 'Loose, watery, and more-frequent bowel movements.',
    },
    {
      name: 'Abdominal pain and cramping',
      description: 'Pain or discomfort in the abdomen.',
    },
    { name: 'Rectal bleeding', description: 'Blood in the stool.' },
    {
      name: 'Weight loss',
      description: 'Unintentional decrease in body weight.',
    },
    {
      name: 'Fatigue',
      description: 'Persistent feeling of tiredness or weakness.',
    },
  ];

  // Check if symptoms already exist before creating
  const existingSymptoms = await prisma.symptom.findMany({
    where: {
      name: { in: symptoms.map(s => s.name) }
    }
  });

  if (existingSymptoms.length === 0) {
    await prisma.symptom.createMany({
      data: symptoms,
      skipDuplicates: true,
    });
    console.log(`Seeded ${symptoms.length} symptoms.`);
  } else {
    console.log(`Symptoms already exist, skipping creation.`);
  }

  const createdSymptoms = await prisma.symptom.findMany();
  const allDiseases = [ibd, ...ibd.subDiseases];
  
  // Check if disease-symptom links already exist
  const existingLinks = await prisma.diseaseSymptom.findMany({
    where: {
      diseaseId: { in: allDiseases.map(d => d.id) }
    }
  });

  if (existingLinks.length === 0) {
    const diseaseSymptomLinks: { diseaseId: string; symptomId: string }[] = [];

    for (const disease of allDiseases) {
      for (const symptom of createdSymptoms) {
        diseaseSymptomLinks.push({
          diseaseId: disease.id,
          symptomId: symptom.id,
        });
      }
    }

    await prisma.diseaseSymptom.createMany({
      data: diseaseSymptomLinks,
      skipDuplicates: true,
    });
    console.log(`Linked symptoms to diseases.`);
  } else {
    console.log(`Disease-symptom links already exist, skipping creation.`);
  }

  console.log(`Seeding public schema finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
